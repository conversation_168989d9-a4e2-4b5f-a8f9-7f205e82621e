<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小学一年级数学 - 10以内加减法</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3436;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            color: #636e72;
            font-size: 1.2em;
        }
        
        .section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-5px);
        }
        
        .section h2 {
            color: #2d3436;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #74b9ff;
        }
        
        .section h3 {
            color: #0984e3;
            font-size: 1.4em;
            margin: 20px 0 10px 0;
        }
        
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .objectives {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .objective-card {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
        }
        
        .objective-card h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .math-problem {
            background: #fff3e0;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #ff9800;
            font-size: 1.3em;
            text-align: center;
        }
        
        .practice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .practice-item {
            background: #e8f5e8;
            padding: 15px;
            text-align: center;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }
        
        .interactive-note {
            background: #f3e5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid #9c27b0;
        }
        
        .interactive-note h4 {
            color: #7b1fa2;
            margin-bottom: 10px;
        }
        
        .footer {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .homework-card {
            background: #e1f5fe;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #00bcd4;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 教学目标 -->
        <div class="header">
            <h1>🧮 小学一年级数学教学</h1>
            <p>10以内加减法 - 让数学变得有趣简单</p>
        </div>

        <div class="section">
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2Ffl8jb530nl.jpeg?Expires=1754470031&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=oQTvGqNalRsy44AVNfdPU3kVGps%3D" alt="教学目标" width="400">
            </div>
            
            <h2>🎯 一、教学目标</h2>
            <div class="objectives">
                <div class="objective-card">
                    <h4>📚 知识与技能</h4>
                    <p>让学生熟练掌握10以内加减法的计算方法，能正确计算相关题目。</p>
                </div>
                <div class="objective-card">
                    <h4>🔧 过程与方法</h4>
                    <p>通过情境创设、动手操作等方式，培养学生的观察能力和计算能力。</p>
                </div>
                <div class="objective-card">
                    <h4>❤️ 情感态度与价值观</h4>
                    <p>激发学生对数学的兴趣，让学生感受数学与生活的联系。</p>
                </div>
            </div>
        </div>

        <!-- 课堂导入 -->
        <div class="section">
            <h2>🌟 二、课堂导入</h2>
            
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2Fzipunhp5yk.jpeg?Expires=1754470039&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=uAE3i9ZPXK5tR%2F0DOqtA%2BAW7bvA%3D" alt="动物园场景" width="400">
            </div>
            
            <div class="interactive-note">
                <h4>🎪 情境创设</h4>
                <p><strong>老师：</strong>"小朋友们，欢迎来到我们的数学动物园！你们看，动物园里有多少只猴子呀？有多少只兔子呢？"</p>
                <p><strong>学生观察：</strong>数一数图片中的小动物</p>
                <p><strong>引入课题：</strong>"今天我们就来学习和这些小动物数量有关的数学知识——10以内的加减法！"</p>
            </div>
        </div>

        <!-- 新知讲授 -->
        <div class="section">
            <h2>📖 三、新知讲授</h2>
            
            <h3>➕ 加法的认识</h3>
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2F0m2t99mq7o.jpeg?Expires=1754470047&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=i4tishCB9ENbdHwhQzxbc7%2FcXZE%3D" alt="加法示例" width="400">
            </div>
            
            <div class="interactive-note">
                <h4>🍎 小明的苹果</h4>
                <p>小明原来有2个苹果，妈妈又给了他3个苹果。现在小明一共有几个苹果呢？</p>
            </div>
            
            <div class="math-problem">
                2 + 3 = 5
                <br><small>把两个部分合起来，求一共有多少，用加法计算</small>
            </div>

            <h3>➖ 减法的认识</h3>
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2Faude84d65p.jpeg?Expires=1754470054&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=nQbc7Ie6ZlUn1D9Nlg8ctsJ2J6g%3D" alt="减法示例" width="400">
            </div>
            
            <div class="interactive-note">
                <h4>🍬 小红的糖果</h4>
                <p>小红有7颗糖果，她吃掉了2颗。现在小红还剩几颗糖果呢？</p>
            </div>
            
            <div class="math-problem">
                7 - 2 = 5
                <br><small>从总数里去掉一部分，求剩下多少，用减法计算</small>
            </div>

            <h3>🔢 计算方法讲解</h3>
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2Fkh5vjdsoa6.jpeg?Expires=1754470465&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=swYX8USE8uOQrippEcQeYJu2N0w%3D" alt="计算工具" width="400">
            </div>
            
            <div class="interactive-note">
                <h4>📏 用小棒计算</h4>
                <p><strong>加法：</strong>先拿3根黄色小棒，再拿4根蓝色小棒，合在一起数出7根</p>
                <p><strong>减法：</strong>用手指计算5-2，伸出5根手指，弯下2根，剩下3根</p>
            </div>
        </div>

        <!-- 练习巩固 -->
        <div class="section">
            <h2>✏️ 四、练习巩固</h2>
            
            <h3>📝 基础练习</h3>
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2F9gshthl699.jpeg?Expires=1754470068&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=CgEKIQrDuV5eT1ozHcHm6uRBkkk%3D" alt="练习题卡" width="400">
            </div>
            
            <div class="practice-grid">
                <div class="practice-item">2 + 5 = ?</div>
                <div class="practice-item">8 - 3 = ?</div>
                <div class="practice-item">4 + 4 = ?</div>
                <div class="practice-item">6 - 1 = ?</div>
                <div class="practice-item">3 + 2 = ?</div>
                <div class="practice-item">9 - 4 = ?</div>
                <div class="practice-item">1 + 6 = ?</div>
                <div class="practice-item">7 - 5 = ?</div>
            </div>

            <h3>🖼️ 看图列式</h3>
            <div class="image-container">
                <img src="https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250806%2Fi28m3xvulb.jpeg?Expires=1754470078&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=AcQ89T4c%2F8RzSqYf5O3TApStrPA%3D" alt="看图列式" width="400">
            </div>
            
            <div class="interactive-note">
                <h4>🐦 小鸟问题</h4>
                <p>树上有4只鸟，又飞来3只，现在一共有几只鸟？</p>
                <p><strong>算式：</strong>4 + 3 = 7</p>
            </div>
            
            <div class="interactive-note">
                <h4>🍪 饼干问题</h4>
                <p>盒子里有9块饼干，拿走了5块，还剩几块饼干？</p>
                <p><strong>算式：</strong>9 - 5 = 4</p>
            </div>

            <h3>🎮 互动游戏 - 找朋友</h3>
            <div class="interactive-note">
                <h4>🎵 游戏规则</h4>
                <p>将结果相同的数字和算式连起来，找到它们的"朋友"</p>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; margin-top: 15px;">
                    <div style="background: #ffeb3b; padding: 10px; margin: 5px; border-radius: 5px;">3 + 2</div>
                    <div style="background: #8bc34a; padding: 10px; margin: 5px; border-radius: 5px;">5</div>
                    <div style="background: #ffeb3b; padding: 10px; margin: 5px; border-radius: 5px;">7 - 2</div>
                    <div style="background: #8bc34a; padding: 10px; margin: 5px; border-radius: 5px;">8</div>
                    <div style="background: #ffeb3b; padding: 10px; margin: 5px; border-radius: 5px;">4 + 4</div>
                </div>
            </div>
        </div>

        <!-- 课堂总结 -->
        <div class="section">
            <h2>📋 五、课堂总结</h2>
            
            <div class="interactive-note">
                <h4>🌟 今天我们学会了什么？</h4>
                <p>✅ 认识了加法和减法的含义</p>
                <p>✅ 学会了10以内加减法的计算方法</p>
                <p>✅ 能够用小棒和手指进行计算</p>
                <p>✅ 会看图列算式解决问题</p>
            </div>
            
            <div class="math-problem">
                🎉 小朋友们今天表现都很棒！<br>
                学会了10以内的加减法，<br>
                以后在生活中也要多多运用哦！
            </div>
        </div>

        <!-- 作业布置 -->
        <div class="section">
            <h2>📚 六、作业布置</h2>
            
            <div class="homework-card">
                <h4>📖 课本练习</h4>
                <p>完成课本上第XX页的练习题，认真计算每一道题目</p>
            </div>
            
            <div class="homework-card">
                <h4>👨‍👩‍👧‍👦 亲子游戏</h4>
                <p>回家后和爸爸妈妈一起玩10以内加减法的口算游戏，看谁算得又快又准确！</p>
            </div>
            
            <div class="homework-card">
                <h4>🔍 生活观察</h4>
                <p>观察生活中需要用到加减法的地方，比如买东西、数玩具等</p>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <h3>🏆 学习小贴士</h3>
            <p>数学就在我们身边，多观察、多思考、多练习，你一定会成为数学小能手！</p>
            <p style="margin-top: 15px; color: #74b9ff;">💝 记住：每一个小进步都值得鼓励！</p>
        </div>
    </div>

    <script>
        // 添加一些简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为练习题添加点击效果
            const practiceItems = document.querySelectorAll('.practice-item');
            practiceItems.forEach(item => {
                item.addEventListener('click', function() {
                    const problem = this.textContent;
                    const answer = eval(problem.replace('= ?', ''));
                    this.innerHTML = problem.replace('?', answer);
                    this.style.background = '#c8e6c9';
                });
            });

            // 添加滚动动画效果
            const sections = document.querySelectorAll('.section');
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
